{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@libs/internal/settings": ["libs/internal/settings/src"], "@libs/internal/settings/*": ["libs/internal/settings/src/*"], "@lib/internal/turnkey": ["libs/internal/turnkey/src"], "@lib/internal/turnkey/*": ["libs/internal/turnkey/src/*"], "@lib/mqtt": ["libs/mqtt/src"], "@lib/mqtt/*": ["libs/mqtt/src/*"], "@lib/redis": ["libs/redis/src"], "@lib/redis/*": ["libs/redis/src/*"], "@libs/internal/vault-management": ["libs/internal/vault-management/src"], "@libs/internal/vault-management/*": ["libs/internal/vault-management/src/*"], "@libs/logger": ["libs/logger/src"], "@libs/logger/*": ["libs/logger/src/*"], "@libs/test": ["libs/test/src"], "@libs/test/*": ["libs/test/src/*"], "@protogen/*": ["protogen/ts/*"]}}}
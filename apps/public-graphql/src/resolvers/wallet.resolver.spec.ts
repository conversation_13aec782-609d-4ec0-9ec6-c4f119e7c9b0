import { Test, TestingModule } from '@nestjs/testing';
import { WalletResolver } from './wallet.resolver';
import { TurnkeyService } from '@lib/internal/turnkey';
import { HyperLiquidService } from 'libs/internal/hyper-liquid/hyper-liquid.service';
import { UsersService } from 'libs/internal/users/users.service';
import {
    ExportPrivateKeyWithoutVerifyInput,
    ApprovedPrivateKeyResponse,
} from '@lib/internal/turnkey/dto/turnkey-auth.dto';

describe('WalletResolver', () => {
    let resolver: WalletResolver;
    let turnkeyService: jest.Mocked<TurnkeyService>;

    beforeEach(async () => {
        const mockTurnkeyService = {
            approveExportPrivateKeyWithoutVerify: jest.fn(),
        };

        const mockHyperLiquidService = {};
        const mockUsersService = {};

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                WalletResolver,
                {
                    provide: TurnkeyService,
                    useValue: mockTurnkeyService,
                },
                {
                    provide: HyperLiquidService,
                    useValue: mockHyperLiquidService,
                },
                {
                    provide: UsersService,
                    useValue: mockUsersService,
                },
            ],
        }).compile();

        resolver = module.get<WalletResolver>(WalletResolver);
        turnkeyService = module.get(TurnkeyService);
    });

    it('should be defined', () => {
        expect(resolver).toBeDefined();
    });

    describe('approveExportPrivateKeyWithoutVerify', () => {
        it('should call turnkeyService.approveExportPrivateKeyWithoutVerify with correct parameters', async () => {
            // Arrange
            const userId = 'test-user-id';
            const input: ExportPrivateKeyWithoutVerifyInput = {
                publicKey: 'test-public-key',
                activityId: 'test-activity-id',
            };
            const expectedResponse: ApprovedPrivateKeyResponse = {
                privateKey: 'test-private-key',
                activityId: 'test-activity-id',
            };
            const context = {
                req: {
                    user: {
                        sub: userId,
                    },
                },
            };

            turnkeyService.approveExportPrivateKeyWithoutVerify.mockResolvedValue(expectedResponse);

            // Act
            const result = await resolver.approveExportPrivateKeyWithoutVerify(context, input);

            // Assert
            expect(turnkeyService.approveExportPrivateKeyWithoutVerify).toHaveBeenCalledWith(userId, input);
            expect(result).toEqual(expectedResponse);
        });

        it('should throw error when turnkeyService throws error', async () => {
            // Arrange
            const userId = 'test-user-id';
            const input: ExportPrivateKeyWithoutVerifyInput = {
                publicKey: 'test-public-key',
                activityId: 'test-activity-id',
            };
            const context = {
                req: {
                    user: {
                        sub: userId,
                    },
                },
            };
            const error = new Error('Service error');

            turnkeyService.approveExportPrivateKeyWithoutVerify.mockRejectedValue(error);

            // Act & Assert
            await expect(resolver.approveExportPrivateKeyWithoutVerify(context, input)).rejects.toThrow(
                'Service error',
            );
        });
    });
});

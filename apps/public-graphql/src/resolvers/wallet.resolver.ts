import { Args, Context, Mutation, Resolver } from '@nestjs/graphql';
import { Req, UseGuards } from '@nestjs/common';
import { GqlAuthGuard } from 'libs/internal/auth/auth.guard';
import { HyperLiquidService } from 'libs/internal/hyper-liquid/hyper-liquid.service';
import { PerpetualStatusDTO, InputPerpetualStatusDTO } from 'libs/internal/hyper-liquid/dto/perpetual-status.dto';
import {
    InputSignCancelOrderDTO,
    InputSignCreateOrderDTO,
    InputSignApproveAgentDTO,
    InputSignUpdateLeverageDTO,
    SignedCancelOrderDTO,
    SignedCreateOrderDTO,
    SignUpdateLeverageResponseDTO,
    SignatureDTO,
    InputSignApproveFeeBuilderDTO,
    InputSignApproveReferralDTO,
} from 'libs/internal/hyper-liquid/dto/hyper-liquid-transaction.dto';
import { TurnkeyService } from '@lib/internal/turnkey';
import {
    ApproveCreateWalletInput,
    ApprovedCreateWalletResponse,
    ApprovedPassphraseResponse,
    ApprovedPrivateKeyResponse,
    ExportPassphraseInput,
    ExportPrivateKeyInput,
    ExportPrivateKeyWithoutVerifyInput,
    InitOtpAuthResponseDto,
    MarkedAsExportedPassphraseResponse,
    RequestReverifyOtpInputDto,
} from '@lib/internal/turnkey/dto/turnkey-auth.dto';
import { UsersService } from 'libs/internal/users/users.service';
import {
    UpdateEmbeddedWalletNameInputDTO,
    UpdateEmbeddedWalletNameResponseDTO,
} from 'libs/internal/users/dto/embedded-wallet.dto';

@Resolver()
@UseGuards(GqlAuthGuard)
export class WalletResolver {
    constructor(
        private readonly hyperLiquidService: HyperLiquidService,
        private readonly turnkeyService: TurnkeyService,
        private readonly usersService: UsersService,
    ) {}

    @Mutation(() => PerpetualStatusDTO)
    public async checkHyperLiquidWallet(@Context() context): Promise<PerpetualStatusDTO> {
        return this.hyperLiquidService.getUserPerpetualStatus(context.req.user.sub);
    }

    @Mutation(() => PerpetualStatusDTO)
    public async updateHyperLiquidWallet(
        @Context() context,
        @Args('input') input: InputPerpetualStatusDTO,
    ): Promise<PerpetualStatusDTO> {
        return this.hyperLiquidService.updateUserPerpetualStatus(context.req.user.sub, input);
    }

    @Mutation(() => SignedCreateOrderDTO)
    public async signHyperLiquidCreateOrder(
        @Context() context,
        @Args('input') input: InputSignCreateOrderDTO,
    ): Promise<SignedCreateOrderDTO> {
        return this.hyperLiquidService.signUserPlaceOrderPublic(context.req.user.sub, input);
    }

    @Mutation(() => SignedCancelOrderDTO)
    public async signHyperLiquidCancelOrder(
        @Context() context,
        @Args('input') input: InputSignCancelOrderDTO,
    ): Promise<SignedCancelOrderDTO> {
        return this.hyperLiquidService.signUserCancelOrderPublic(context.req.user.sub, input);
    }

    @Mutation(() => SignedCancelOrderDTO)
    public async signHyperLiquidUpdateLeverage(
        @Context() context,
        @Args('input') input: InputSignUpdateLeverageDTO,
    ): Promise<SignUpdateLeverageResponseDTO> {
        return this.hyperLiquidService.signUpdateLeverage(context.req.user.sub, input);
    }

    @Mutation(() => SignatureDTO)
    public async approveHyperLiquidApproveAgent(
        @Context() context,
        @Args('input') input: InputSignApproveAgentDTO,
    ): Promise<SignatureDTO> {
        return this.hyperLiquidService.acceptApproveAgentActivity(context.req.user.sub, input);
    }

    @Mutation(() => SignatureDTO)
    public async approveHyperLiquidFeeBuilder(
        @Context() context,
        @Args('input') input: InputSignApproveFeeBuilderDTO,
    ): Promise<SignatureDTO> {
        return this.hyperLiquidService.acceptApproveFeeBuilderActivity(context.req.user.sub, input);
    }

    @Mutation(() => SignatureDTO)
    public async approveHyperLiquidReferral(
        @Context() context,
        @Args('input') input: InputSignApproveReferralDTO,
    ): Promise<SignatureDTO> {
        return this.hyperLiquidService.acceptApproveReferralActivity(context.req.user.sub, input);
    }

    @Mutation(() => ApprovedPassphraseResponse)
    async approveExportPassphrase(
        @Context() context,
        @Args('input') input: ExportPassphraseInput,
    ): Promise<ApprovedPassphraseResponse> {
        const userId = context.req.user.sub;
        return this.turnkeyService.approveExportPassphrase(userId, input);
    }

    @Mutation(() => ApprovedPrivateKeyResponse)
    async approveExportPrivateKey(
        @Context() context,
        @Args('input') input: ExportPrivateKeyInput,
    ): Promise<ApprovedPrivateKeyResponse> {
        const userId = context.req.user.sub;
        return this.turnkeyService.approveExportAccountPrivateKey(userId, input);
    }

    @Mutation(() => ApprovedPrivateKeyResponse)
    async approveExportPrivateKeyWithoutVerify(
        @Context() context,
        @Args('input') input: ExportPrivateKeyWithoutVerifyInput,
    ): Promise<ApprovedPrivateKeyResponse> {
        const userId = context.req.user.sub;
        return this.turnkeyService.approveExportPrivateKeyWithoutVerify(userId, input);
    }

    @Mutation(() => MarkedAsExportedPassphraseResponse)
    async markedAsExportedPassphrase(@Context() context): Promise<MarkedAsExportedPassphraseResponse> {
        const userId = context.req.user.sub;
        const updated = await this.turnkeyService.updateUserExportedWallet(userId);

        return {
            updated,
        };
    }

    @Mutation(() => InitOtpAuthResponseDto)
    async requestReverifyOtp(
        @Context() context,
        @Args('input') input: RequestReverifyOtpInputDto,
    ): Promise<InitOtpAuthResponseDto> {
        const userId = context.req.user.sub;
        return this.turnkeyService.requestReverifyOtp(userId, input);
    }

    @Mutation(() => UpdateEmbeddedWalletNameResponseDTO)
    async updateEmbeddedWalletName(
        @Context() context,
        @Args('input') input: UpdateEmbeddedWalletNameInputDTO,
    ): Promise<UpdateEmbeddedWalletNameResponseDTO> {
        try {
            const userId = context.req.user.sub;
            const updatedWallet = await this.usersService.updateEmbeddedWalletName(userId, input);

            return {
                success: true,
                message: 'Wallet name updated successfully',
                wallet: updatedWallet,
            };
        } catch (error) {
            return {
                success: false,
                message: error.message || 'Failed to update wallet name',
                wallet: null,
            };
        }
    }

    @Mutation(() => ApprovedCreateWalletResponse)
    async approveCreateWallet(
        @Context() context,
        @Args('input') input: ApproveCreateWalletInput,
    ): Promise<ApprovedCreateWalletResponse> {
        const userId = context.req.user.sub;
        return this.turnkeyService.approveCreateWallet(userId, input);
    }
}

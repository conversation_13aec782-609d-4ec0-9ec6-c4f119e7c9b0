import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { GraphQLExceptionFilter } from '../../../libs/common/filters/graphql-exception.filter';
import { getBotToken } from 'nestjs-telegraf';
import { appConfig } from '../../../libs/configs';
import { ResponseTimeInterceptor } from 'libs/common/interceptors/response-time.interceptor';
import { PinoLogger } from 'nestjs-pino';

async function bootstrap() {
    const app = await NestFactory.create(AppModule);

    const bot = app.get(getBotToken(appConfig.TELEGRAM_BOT_AUTH_NAME));
    // app.use(bot.webhookCallback(`/telegram-bot/${appConfig.TELEGRAM_BOT_AUTH_NAME}/${appConfig.TELEGRAM_BOT_WEBHOOK}`));
    app.enableCors({
        origin: function (origin: any, callback: any) {
            if (!origin || appConfig.CORS_ORIGINS[0] === '*') return callback(null, true);
            if (!appConfig.CORS_ORIGINS.includes(origin)) {
                const msg = 'The CORS policy for this site does not allow access from the specified Origin.';
                return callback(new Error(msg), false);
            }
            return callback(null, true);
        },
        allowedHeaders: ['Authorization', '*'],
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
        exposedHeaders: ['*'],
        credentials: true,
        maxAge: appConfig.CORS_MAX_AGE_HOUR * 60 * 60,
    });
    app.use(
        bot.webhookCallback(
            `/api/user/webhook/telegram-bot/${appConfig.TELEGRAM_BOT_AUTH_NAME}/${appConfig.TELEGRAM_BOT_WEBHOOK}`,
        ),
    );

    const logger = await app.resolve(PinoLogger);
    app.useGlobalFilters(new GraphQLExceptionFilter(logger));
    app.useGlobalInterceptors(new ResponseTimeInterceptor(logger));

    const port = process.env.PORT ?? 3000;
    await app.listen(port);
    console.log(`Listening on port ${port}`);
}

bootstrap().catch((err) => {
    console.error(err);
});

import { TurnkeyService } from '@lib/internal/turnkey';
import { Injectable } from '@nestjs/common';
import {
    ApproveWithdrawRequest,
    ApproveWithdrawResponse,
    ChainType as RequestChainType,
    SignUserTransactionRequest,
    SignUserTransactionResponse,
} from '@protogen/user/v1/signing';
import { ChainType } from 'libs/internal/users/entities/user-managed-wallet.entity';

@Injectable()
export class UserSigningService {
    constructor(private readonly turnKeyService: TurnkeyService) {}

    async signTransaction(request: SignUserTransactionRequest): Promise<SignUserTransactionResponse> {
        let chainType = ChainType.EVM;
        switch (request.chain) {
            case RequestChainType.EVM:
                chainType = ChainType.EVM;
                break;
            case RequestChainType.ARB:
                chainType = ChainType.ARB;
                break;
            case RequestChainType.SOLANA:
                chainType = ChainType.SOLANA;
                break;
            case RequestChainType.TRON:
                chainType = ChainType.TRON;
                break;
            default:
                break;
        }
        const signedTransaction = await this.turnKeyService.signUserTransaction(
            request.userId,
            chainType,
            request.unsignedTransaction,
            request.address,
        );

        return {
            signedTransaction,
        };
    }

    async approveWithdraw(request: ApproveWithdrawRequest): Promise<ApproveWithdrawResponse> {
        const signedTransaction = await this.turnKeyService.withdrawTransaction(request);

        return {
            signedTransaction,
        };
    }
}

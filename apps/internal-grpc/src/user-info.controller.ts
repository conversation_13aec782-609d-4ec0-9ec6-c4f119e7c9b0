import { Controller } from '@nestjs/common';
import {
    AuthCodeResponse,
    GetAccessTokenResponse,
    GetUserBalanceRequest,
    GetUserInfoRequest,
    GetUserReferrerCodeRequest,
    GetUserReferrerCodeResponse,
    UserBanlanceResponse,
    UserInfoResponse,
    UserInfoServiceControllerMethods,
    WalletManagedResponse,
} from '@protogen/user/v1/user_info';
import { TelegramAuthService } from 'libs/internal/auth/telegram-auth.service';
import { UserInfoService } from 'libs/internal/users/user-info.service';
import { UsersService } from 'libs/internal/users/users.service';
import { JwtService } from '@nestjs/jwt';
import { RpcException } from '@nestjs/microservices';
import { UnauthorizedException } from '@nestjs/common';
import { Metadata } from '@grpc/grpc-js';
import { JwtPayload } from '../utils/auth/jwt';

@Controller()
@UserInfoServiceControllerMethods()
export class UserInfoController {
    constructor(
        private readonly userService: UsersService,
        private readonly userInfoService: UserInfoService,
        private readonly telegramAuthService: TelegramAuthService,
        private readonly jwtService: JwtService,
    ) {}
    async getUserInfo(request: GetUserInfoRequest): Promise<UserInfoResponse | null> {
        console.log('GetUserInfoRequest: ', request);
        return this.userService.getUserInfoByTelegram(request);
    }

    async getUserWalletManaged(request: GetUserInfoRequest): Promise<WalletManagedResponse> {
        return this.userInfoService.getUserWalletManaged(request);
    }

    async getUserBalance(request: GetUserBalanceRequest): Promise<UserBanlanceResponse> {
        return this.userInfoService.getUserBalance(request);
    }

    async getAccessToken(request: GetUserInfoRequest): Promise<GetAccessTokenResponse> {
        const { accessToken, userId } = await this.telegramAuthService.getAccessToken(
            request.telegramId,
            request.userId,
        );

        return {
            accessToken,
            userId,
        };
    }

    async getAuthCode(request: GetUserInfoRequest): Promise<AuthCodeResponse> {
        const { code, userId } = await this.telegramAuthService.getAuthCode(request.telegramId, request.userId);
        return {
            authCode: code,
            userId: userId,
        };
    }

    async getUserReferrerCode(
        request: GetUserReferrerCodeRequest,
        metadata: Metadata,
    ): Promise<GetUserReferrerCodeResponse> {
        let token: string | undefined;
        if (metadata && metadata.get) {
            const authHeader = metadata.get('authorization')[0];
            if (authHeader && typeof authHeader === 'string' && authHeader.startsWith('Bearer ')) {
                token = authHeader.slice(7);
            }
        }
        if (!token) {
            throw new RpcException(new UnauthorizedException('Missing JWT token'));
        }
        const payload = this.jwtService.decode<JwtPayload>(token);
        const userId = payload.sub;
        if (!userId) {
            throw new RpcException(new UnauthorizedException('Invalid JWT payload'));
        }
        return this.userService.getUserReferrerCode(userId);
    }
}

import { NestFactory } from '@nestjs/core';
import { InternalGrpcModule } from './internal-grpc.module';
import { join } from 'path';
import { appConfig } from '../../../libs/configs';
import { Transport } from '@nestjs/microservices';
import { USER_HYPERLIQUID_VAULT_V1_PACKAGE_NAME } from 'protogen/ts/user/v1/hyper_liquid_vault';
import { ReflectionService } from '@grpc/reflection';
import { ResponseTimeInterceptor } from 'libs/common/interceptors/response-time.interceptor';
import { PinoLogger } from 'nestjs-pino';
import { USER_USER_INFO_V1_PACKAGE_NAME } from '@protogen/user/v1/user_info';
import { USER_HYPERLIQUID_USER_V1_PACKAGE_NAME } from '@protogen/user/v1/hyper_liquid_user';
import { USER_SIGNING_V1_PACKAGE_NAME } from '@protogen/user/v1/signing';

async function bootstrap() {
    const path = join(__dirname, '../../../../../../proto');

    const port = appConfig.INTERNAL_GRPC_PORT;
    const app = await NestFactory.createMicroservice(InternalGrpcModule, {
        transport: Transport.GRPC,
        options: {
            url: `0.0.0.0:${port}`,
            package: [
                USER_HYPERLIQUID_VAULT_V1_PACKAGE_NAME,
                USER_HYPERLIQUID_USER_V1_PACKAGE_NAME,
                USER_USER_INFO_V1_PACKAGE_NAME,
                USER_SIGNING_V1_PACKAGE_NAME,
            ],
            protoPath: [
                `${path}/user/v1/hyper_liquid_vault.proto`,
                `${path}/user/v1/hyper_liquid_user.proto`,
                `${path}/user/v1/user_info.proto`,
                `${path}/user/v1/signing.proto`,
            ],
            loader: {
                includeDirs: [path],
            },
            keepCase: false,
            onLoadPackageDefinition: (pkg, server) => {
                new ReflectionService(pkg).addToServer(server);
            },
            gracefulShutdown: true,
        },
    });

    const logger = await app.resolve(PinoLogger);
    app.useGlobalInterceptors(new ResponseTimeInterceptor(logger));

    app.enableShutdownHooks();
    await app.listen();
    console.log(`gRPC server is running on port ${port}`);
}

bootstrap();

# OTP Verification Throttling

## Tổng quan

Hệ thống throttling cho OTP verification được thiết kế để ngăn chặn các cuộc tấn công brute force bằng cách giới hạn số lần thử sai OTP cho mỗi OTP_ID.

## C<PERSON> chế hoạt động

### Cấu hình mặc định
- **Số lần thử tối đa**: 5 lần sai trong 15 phút
- **Thời gian khóa**: 15 phút
- **Phạm vi áp dụng**: <PERSON> từng OTP_ID (không ảnh hưởng lẫn nhau)

### Quy trình throttling

1. **Kiểm tra trước khi verify**: Hệ thống kiểm tra xem OTP_ID đã bị throttle chưa
2. **Ghi nhận lỗi**: Mỗi lần verify OTP thất bại sẽ được ghi nhận
3. **Kích hoạt throttling**: Sau 5 lần thất bại, OTP_ID sẽ bị khóa trong 15 phút
4. **Thông báo cụ thể**: Error message sẽ hiển thị thời gian chờ chính xác

## Cấu hình

### Constants (libs/internal/rate-limiting/src/rate-limiting.constants.ts)
```typescript
// OTP verification throttling to prevent brute force attacks
export const OTP_VERIFICATION_THROTTLE_WINDOW_MS = 15 * 60 * 1000; // 15 minutes
export const OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS = 5; // 5 failed attempts per OTP_ID
```

### Error Message (libs/common/api-errors/errors.ts)
```typescript
export const OTP_VERIFICATION_THROTTLED: IApiError = {
    code: 'OTP_VERIFICATION_THROTTLED',
    message: 'Too many failed OTP verification attempts. Please wait {waitTime} before trying again',
};
```

## Các endpoint được bảo vệ

### 1. loginWithEmailOtp
- **Endpoint**: `loginWithEmailOtp` mutation
- **Throttling key**: `otp_verification_throttle:{hashed_otp_id}`
- **Hành vi**: 
  - Kiểm tra throttling trước khi verify OTP
  - Ghi nhận failed attempt nếu verification thất bại
  - Trả về error với thời gian chờ cụ thể

### 2. reverifyUserAuthentication
- **Sử dụng trong**: Các thao tác nhạy cảm như export private key
- **Throttling key**: `otp_verification_throttle:{hashed_otp_id}`
- **Hành vi**: Tương tự như loginWithEmailOtp

## Cấu trúc Redis

### Key pattern
```
otp_verification_throttle:{hashed_otp_id}
```

### Data structure
- **Type**: Sorted Set (ZSET)
- **Score**: Timestamp của failed attempt
- **Value**: Unique identifier cho mỗi attempt
- **TTL**: 15 phút (tự động cleanup)

### Ví dụ Redis data
```
Key: otp_verification_throttle:abc123def456...
Score: 1703123456789 | Value: 1703123456789-0.123456
Score: 1703123457890 | Value: 1703123457890-0.234567
...
```

## API Response Examples

### Successful verification
```json
{
  "data": {
    "loginWithEmailOtp": {
      "accessToken": "...",
      "refreshToken": "...",
      "userId": "...",
      "subOrgId": "..."
    }
  }
}
```

### Failed verification (chưa bị throttle)
```json
{
  "errors": [
    {
      "message": "OTP verification failed",
      "extensions": {
        "code": "OTP_VERIFICATION_FAILED"
      }
    }
  ]
}
```

### Throttled verification
```json
{
  "errors": [
    {
      "message": "Too many failed OTP verification attempts. Please wait 12 minutes before trying again",
      "extensions": {
        "code": "OTP_VERIFICATION_THROTTLED"
      }
    }
  ]
}
```

## Testing

### Chạy test script
```bash
node test-otp-throttling.js
```

### Test cases
1. **Basic throttling**: Verify rằng sau 5 lần thất bại, OTP_ID bị khóa
2. **Isolation**: Verify rằng throttling của OTP_ID này không ảnh hưởng OTP_ID khác
3. **Wait time accuracy**: Verify rằng thời gian chờ được hiển thị chính xác
4. **Auto cleanup**: Verify rằng throttling tự động hết hạn sau 15 phút

## Monitoring & Logging

### Log entries
```typescript
// Throttling activated
this.logger.warn('OTP verification throttled', {
  otpId: 'abc123...',
  resetTime: '2023-12-21T10:30:00.000Z',
  waitTimeMinutes: 12,
  totalAttempts: 5,
});

// Failed attempt recorded
this.logger.info('Failed OTP verification attempt recorded', {
  hashedOtpId: 'def456...',
  keyPrefix: 'otp_verification_throttle',
});
```

### Metrics to monitor
- Số lượng OTP bị throttle per day
- Thời gian trung bình giữa các failed attempts
- Tỷ lệ success/failure sau khi throttling được kích hoạt

## Security Benefits

1. **Ngăn chặn brute force**: Giới hạn số lần thử đoán OTP
2. **Bảo vệ theo OTP_ID**: Mỗi OTP session được bảo vệ riêng biệt
3. **Privacy**: OTP_ID được hash trước khi lưu Redis
4. **Thông báo rõ ràng**: User biết chính xác thời gian cần chờ
5. **Auto cleanup**: Không tích tụ data cũ trong Redis

## Cấu hình nâng cao

### Thay đổi thời gian throttling
```typescript
// Trong constants file
export const OTP_VERIFICATION_THROTTLE_WINDOW_MS = 30 * 60 * 1000; // 30 minutes
export const OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS = 3; // 3 attempts only
```

### Custom error message
```typescript
// Trong errors file
export const OTP_VERIFICATION_THROTTLED: IApiError = {
    code: 'OTP_VERIFICATION_THROTTLED',
    message: 'Quá nhiều lần nhập sai OTP. Vui lòng chờ {waitTime} trước khi thử lại',
};
```

## Troubleshooting

### Common issues
1. **Redis connection**: Đảm bảo Redis service đang chạy
2. **Clock sync**: Đảm bảo server time được sync chính xác
3. **Memory usage**: Monitor Redis memory usage với nhiều throttled OTPs

### Debug commands
```bash
# Check Redis keys
redis-cli KEYS "otp_verification_throttle:*"

# Check specific OTP throttling
redis-cli ZRANGE "otp_verification_throttle:abc123..." 0 -1 WITHSCORES

# Clear throttling for testing
redis-cli DEL "otp_verification_throttle:abc123..."
```

#!/usr/bin/env node

/**
 * Test script for rate limiting functionality in initOtpAuth
 * This script tests the email and fingerprint rate limiting
 */

const axios = require('axios');

const GRAPHQL_ENDPOINT = 'http://localhost:3000/graphql';

const INIT_EMAIL_OTP_MUTATION = `
  mutation InitEmailOtp($input: InitEmailOtpInputDTO!) {
    initEmailOtp(input: $input) {
      otpId
      userId
      subOrgId
      ttl
    }
  }
`;

async function makeGraphQLRequest(query, variables) {
  try {
    const response = await axios.post(GRAPHQL_ENDPOINT, {
      query,
      variables,
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    return response.data;
  } catch (error) {
    if (error.response) {
      return error.response.data;
    }
    throw error;
  }
}

async function testEmailRateLimit() {
  console.log('\n=== Testing Email Rate Limiting ===');
  
  const testEmail = '<EMAIL>';
  const input = {
    email: testEmail,
    fingerprint: 'test-fingerprint-1',
  };

  // First request should succeed
  console.log('Making first request...');
  const result1 = await makeGraphQLRequest(INIT_EMAIL_OTP_MUTATION, { input });
  console.log('First request result:', JSON.stringify(result1, null, 2));

  // Second request within 1 minute should be rate limited
  console.log('\nMaking second request immediately...');
  const result2 = await makeGraphQLRequest(INIT_EMAIL_OTP_MUTATION, { input });
  console.log('Second request result:', JSON.stringify(result2, null, 2));

  if (result2.errors && result2.errors[0].extensions.code === 'EMAIL_RATE_LIMITED') {
    console.log('✅ Email rate limiting is working correctly!');
  } else {
    console.log('❌ Email rate limiting is not working as expected');
  }
}

async function testFingerprintRateLimit() {
  console.log('\n=== Testing Fingerprint Rate Limiting ===');
  
  const testFingerprint = 'test-fingerprint-unique';
  const input1 = {
    email: '<EMAIL>',
    fingerprint: testFingerprint,
  };
  const input2 = {
    email: '<EMAIL>', // Different email
    fingerprint: testFingerprint, // Same fingerprint
  };

  // First request should succeed
  console.log('Making first request with fingerprint...');
  const result1 = await makeGraphQLRequest(INIT_EMAIL_OTP_MUTATION, { input: input1 });
  console.log('First request result:', JSON.stringify(result1, null, 2));

  // Second request with same fingerprint but different email should be rate limited
  console.log('\nMaking second request with same fingerprint, different email...');
  const result2 = await makeGraphQLRequest(INIT_EMAIL_OTP_MUTATION, { input: input2 });
  console.log('Second request result:', JSON.stringify(result2, null, 2));

  if (result2.errors && result2.errors[0].extensions.code === 'FINGERPRINT_RATE_LIMITED') {
    console.log('✅ Fingerprint rate limiting is working correctly!');
  } else {
    console.log('❌ Fingerprint rate limiting is not working as expected');
  }
}

async function testRateLimitReset() {
  console.log('\n=== Testing Rate Limit Reset After 1 Minute ===');
  console.log('This test will wait for 61 seconds to test rate limit reset...');
  
  const testEmail = '<EMAIL>';
  const input = {
    email: testEmail,
    fingerprint: 'reset-test-fingerprint',
  };

  // First request
  console.log('Making first request...');
  const result1 = await makeGraphQLRequest(INIT_EMAIL_OTP_MUTATION, { input });
  console.log('First request result:', result1.errors ? 'Error' : 'Success');

  // Wait 61 seconds
  console.log('Waiting 61 seconds for rate limit to reset...');
  await new Promise(resolve => setTimeout(resolve, 61000));

  // Request after rate limit reset
  console.log('Making request after rate limit reset...');
  const result2 = await makeGraphQLRequest(INIT_EMAIL_OTP_MUTATION, { input });
  console.log('Request after reset result:', JSON.stringify(result2, null, 2));

  if (!result2.errors) {
    console.log('✅ Rate limit reset is working correctly!');
  } else {
    console.log('❌ Rate limit reset is not working as expected');
  }
}

async function runTests() {
  console.log('Starting Rate Limiting Tests for initOtpAuth');
  console.log('Make sure the GraphQL server is running on http://localhost:3000');
  
  try {
    await testEmailRateLimit();
    await testFingerprintRateLimit();
    
    // Ask user if they want to run the long test
    console.log('\n=== Long Test Warning ===');
    console.log('The next test will take 61 seconds to complete.');
    console.log('Do you want to run it? (y/N)');
    
    // For automated testing, skip the long test
    console.log('Skipping long test for now. Run manually if needed.');
    
  } catch (error) {
    console.error('Test failed with error:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('Make sure the GraphQL server is running on http://localhost:3000');
    }
  }
}

if (require.main === module) {
  runTests();
}

module.exports = {
  testEmailRateLimit,
  testFingerprintRateLimit,
  testRateLimitReset,
};

// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.178.0
//   protoc               unknown
// source: user/v1/signing.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "user.signing.v1";

export enum ChainType {
  EVM = 0,
  SOLANA = 1,
  TRON = 2,
  ARB = 3,
  UNRECOGNIZED = -1,
}

export interface SignUserTransactionEvmRequest {
  userId: string;
  to: string;
  data: string;
  value?: string | undefined;
  chain: ChainType;
  maxFeePerGas?: string | undefined;
  maxPriorityFeePerGas?: string | undefined;
  gasLimit?: string | undefined;
  gasPrice?: string | undefined;
  from: string;
}

export interface SignUserTransactionEvmResponse {
  userId: string;
  to: string;
  data: string;
  value?: string | undefined;
  chain: ChainType;
  maxFeePerGas?: string | undefined;
  maxPriorityFeePerGas?: string | undefined;
  gasLimit?: string | undefined;
  gasPrice?: string | undefined;
  from: string;
  signature: string;
}

export interface SignUserTransactionRequest {
  userId: string;
  unsignedTransaction: string;
  chain: ChainType;
  address: string;
}

export interface SignUserTransactionResponse {
  signedTransaction: string;
}

export interface ApproveWithdrawRequest {
  userId: string;
  chain: ChainType;
  address: string;
  receiveAddress: string;
  amount: string;
  activityId: string;
  token: string;
  decimals: number;
  signature?: string | undefined;
  message?: string | undefined;
  isOkxWallet?: boolean | undefined;
  oidcToken?: string | undefined;
  otpId?: string | undefined;
  otpCode?: string | undefined;
}

export interface ApproveWithdrawResponse {
  signedTransaction: string;
}

export const USER_SIGNING_V1_PACKAGE_NAME = "user.signing.v1";

export interface UserSigningServiceClient {
  signEvmTransaction(
    request: SignUserTransactionEvmRequest,
    metadata?: Metadata,
  ): Observable<SignUserTransactionEvmResponse>;

  signTransaction(request: SignUserTransactionRequest, metadata?: Metadata): Observable<SignUserTransactionResponse>;

  approveWithdrawTransaction(request: ApproveWithdrawRequest, metadata?: Metadata): Observable<ApproveWithdrawResponse>;
}

export interface UserSigningServiceController {
  signEvmTransaction(
    request: SignUserTransactionEvmRequest,
    metadata?: Metadata,
  ):
    | Promise<SignUserTransactionEvmResponse>
    | Observable<SignUserTransactionEvmResponse>
    | SignUserTransactionEvmResponse;

  signTransaction(
    request: SignUserTransactionRequest,
    metadata?: Metadata,
  ): Promise<SignUserTransactionResponse> | Observable<SignUserTransactionResponse> | SignUserTransactionResponse;

  approveWithdrawTransaction(
    request: ApproveWithdrawRequest,
    metadata?: Metadata,
  ): Promise<ApproveWithdrawResponse> | Observable<ApproveWithdrawResponse> | ApproveWithdrawResponse;
}

export function UserSigningServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["signEvmTransaction", "signTransaction", "approveWithdrawTransaction"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("UserSigningService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("UserSigningService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const USER_SIGNING_SERVICE_NAME = "UserSigningService";

// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.178.0
//   protoc               unknown
// source: user/v1/user_info.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "user.user_info.v1";

export enum ChainType {
  EVM = 0,
  SOLANA = 1,
  TRON = 2,
  ARB = 3,
  UNRECOGNIZED = -1,
}

export interface GetUserInfoRequest {
  telegramId: string;
  userId: string;
}

export interface UserInfoResponse {
  id: string;
  /** string wallet_address =2; */
  name?: string | undefined;
  telegramId: string;
  telegramUsername?: string | undefined;
  telegramChatId: number;
}

export interface GetUserWalletManagedRequest {
  id: string;
}

export interface UserManagedWallet {
  id: string;
  userId: string;
  chain: ChainType;
  walletAddress: string;
}

export interface WalletManagedResponse {
  wallets: UserManagedWallet[];
}

export interface GetUserBalanceRequest {
  walletAddress: string;
}

export interface UserBanlanceResponse {
  balance: number;
}

export interface GetAccessTokenResponse {
  accessToken: string;
  userId: string;
}

export interface AuthCodeResponse {
  authCode: string;
  userId: string;
}

export interface GetUserReferrerCodeRequest {
}

export interface GetUserReferrerCodeResponse {
  referrerCode: string;
}

export const USER_USER_INFO_V1_PACKAGE_NAME = "user.user_info.v1";

export interface UserInfoServiceClient {
  getUserInfo(request: GetUserInfoRequest, metadata?: Metadata): Observable<UserInfoResponse>;

  getUserWalletManaged(request: GetUserInfoRequest, metadata?: Metadata): Observable<WalletManagedResponse>;

  getUserBalance(request: GetUserBalanceRequest, metadata?: Metadata): Observable<UserBanlanceResponse>;

  getAccessToken(request: GetUserInfoRequest, metadata?: Metadata): Observable<GetAccessTokenResponse>;

  getAuthCode(request: GetUserInfoRequest, metadata?: Metadata): Observable<AuthCodeResponse>;

  getUserReferrerCode(
    request: GetUserReferrerCodeRequest,
    metadata?: Metadata,
  ): Observable<GetUserReferrerCodeResponse>;
}

export interface UserInfoServiceController {
  getUserInfo(
    request: GetUserInfoRequest,
    metadata?: Metadata,
  ): Promise<UserInfoResponse> | Observable<UserInfoResponse> | UserInfoResponse;

  getUserWalletManaged(
    request: GetUserInfoRequest,
    metadata?: Metadata,
  ): Promise<WalletManagedResponse> | Observable<WalletManagedResponse> | WalletManagedResponse;

  getUserBalance(
    request: GetUserBalanceRequest,
    metadata?: Metadata,
  ): Promise<UserBanlanceResponse> | Observable<UserBanlanceResponse> | UserBanlanceResponse;

  getAccessToken(
    request: GetUserInfoRequest,
    metadata?: Metadata,
  ): Promise<GetAccessTokenResponse> | Observable<GetAccessTokenResponse> | GetAccessTokenResponse;

  getAuthCode(
    request: GetUserInfoRequest,
    metadata?: Metadata,
  ): Promise<AuthCodeResponse> | Observable<AuthCodeResponse> | AuthCodeResponse;

  getUserReferrerCode(
    request: GetUserReferrerCodeRequest,
    metadata?: Metadata,
  ): Promise<GetUserReferrerCodeResponse> | Observable<GetUserReferrerCodeResponse> | GetUserReferrerCodeResponse;
}

export function UserInfoServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getUserInfo",
      "getUserWalletManaged",
      "getUserBalance",
      "getAccessToken",
      "getAuthCode",
      "getUserReferrerCode",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("UserInfoService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("UserInfoService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const USER_INFO_SERVICE_NAME = "UserInfoService";

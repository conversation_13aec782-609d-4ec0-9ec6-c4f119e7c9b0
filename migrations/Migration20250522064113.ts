import { Migration } from '@mikro-orm/migrations';

export class Migration20250522064113 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "real_user" ("id" uuid not null, constraint "real_user_pkey" primary key ("id"));`);

    this.addSql(`alter table "user" add column "real_user_id" uuid null;`);
    this.addSql(`alter table "user" alter column "referrer_code" type varchar(20) using ("referrer_code"::varchar(20));`);
    this.addSql(`alter table "user" alter column "referrer_code" set not null;`);
    this.addSql(`alter table "user" add constraint "user_real_user_id_foreign" foreign key ("real_user_id") references "real_user" ("id") on update cascade on delete set null;`);

    this.addSql(`alter table "user_device" add column "real_user_id" uuid null;`);
    this.addSql(`alter table "user_device" add constraint "user_device_real_user_id_foreign" foreign key ("real_user_id") references "real_user" ("id") on update cascade on delete set null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user" drop constraint "user_real_user_id_foreign";`);

    this.addSql(`alter table "user_device" drop constraint "user_device_real_user_id_foreign";`);

    this.addSql(`drop table if exists "real_user" cascade;`);

    this.addSql(`alter table "user" drop column "real_user_id";`);

    this.addSql(`alter table "user" alter column "referrer_code" type varchar(255) using ("referrer_code"::varchar(255));`);
    this.addSql(`alter table "user" alter column "referrer_code" drop not null;`);

    this.addSql(`alter table "user_device" drop column "real_user_id";`);
  }

}

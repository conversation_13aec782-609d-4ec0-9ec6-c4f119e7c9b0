#--------------------------------------
# Stage: Compile Apps
#--------------------------------------
FROM node:22.4.0-alpine3.19 AS builder
ENV REFRESHED_AT 2019-12-08

# Set the working directory
WORKDIR /app


COPY package*.json ./
COPY yarn.lock ./

RUN yarn

# Copy the rest of the application code
COPY . .
RUN yarn build public-graphql
RUN yarn build internal-grpc

#--------------------------------------
# Stage: Packaging Apps
#--------------------------------------
FROM node:22.4.0-alpine3.19

WORKDIR /app

RUN mkdir src
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/tsconfig.json ./tsconfig.json
COPY --from=builder /app/dist/migrations migrations
COPY --from=builder /app/proto ./proto

COPY package*.json ./
COPY env/ env/

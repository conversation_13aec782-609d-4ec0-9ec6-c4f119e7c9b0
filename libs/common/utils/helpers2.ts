import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import Decimal from 'decimal.js';

// dayjs.extend(utc);

export function formatTokenPrice(
    value: number,
    options?: {
        roundType?: 'round' | 'floor' | 'ceil';
    },
): {
    integerPart: string;
    zeroCount: number;
    decimalPart: string;
    unit?: string;
} {
    const roundType = options?.roundType || 'round';

    const roundFn = (val: number) => {
        switch (roundType) {
            case 'floor':
                return Math.floor(val);
            case 'ceil':
                return Math.ceil(val);
            default:
                return Math.round(val);
        }
    };

    const negative = value < 0;
    const absValue = Math.abs(value);

    if (absValue >= 1_000_000_000) {
        const formattedValue = (absValue / 1_000_000_000).toFixed(4);
        return {
            integerPart: `${negative ? '-' : ''}${formattedValue.replace(/\.0+$/, '').replace(/(\.\d+?)0+$/, '1')}`,
            zeroCount: 0,
            decimalPart: '',
            unit: 'B',
        };
    }

    if (absValue < 1) {
        const str = value.toString();
        const [base, exponent] = str.split('e');

        if (exponent) {
            const exponentValue = parseInt(exponent, 10);
            const zerosToAdd = Math.abs(exponentValue) - 1;
            const [integerPart, decimalPart] = base.split('.');
            let newDecimalPart = '';
            if (decimalPart) {
                const decimalLength = parseInt(integerPart) >= 1 ? 4 : 3;
                const decimalLongerThan4 = decimalPart.length > decimalLength;
                const decimal = decimalLongerThan4
                    ? roundFn(parseFloat(decimalPart.slice(0, decimalLength)) / 10)
                    : parseFloat(decimalPart);
                newDecimalPart = decimalLongerThan4 ? decimal.toString() : decimalPart;
            }
            return {
                integerPart: `${negative ? '-' : ''}0`,
                zeroCount: zerosToAdd,
                decimalPart: `${integerPart}${newDecimalPart}`.replace(/0+$/, ''),
            };
        }

        const decimalIndex = str.indexOf('.');
        const decimalPart = str.slice(decimalIndex + 1);
        const zeroMatch = decimalPart.match(/^0{4,}/);

        if (zeroMatch) {
            const zeroStr = zeroMatch[0];
            const zeroStart = decimalPart.indexOf(zeroStr);
            const afterZeros = decimalPart.slice(zeroStart + zeroStr.length);
            const nextDigits = afterZeros.slice(0, 5);
            const rounded = nextDigits.length >= 4 ? roundFn(parseFloat(nextDigits) / 10) : afterZeros;
            const zeroCount = zeroStr.length;
            return {
                integerPart: `${negative ? '-' : ''}0`,
                zeroCount,
                decimalPart: rounded.toString().replace(/0+$/, ''),
            };
        } else {
            const zeroCount = decimalPart.search(/[^0]/);
            const afterZeros = decimalPart.slice(zeroCount, zeroCount + 5);
            const rounded = afterZeros.length >= 4 ? roundFn(parseFloat(afterZeros) / 10) : afterZeros.toString();
            const zeroString = zeroCount > 0 ? '0'.repeat(zeroCount) : '';
            return {
                integerPart: `${negative ? '-' : ''}0`,
                zeroCount: 0,
                decimalPart: (zeroString + rounded).replace(/0+$/, ''),
            };
        }
    } else {
        const str = value.toString();
        const [integerPart, decimalPart] = str.split('.');
        if (!decimalPart) {
            return {
                integerPart: `${negative ? '-' : ''}${str}`,
                zeroCount: 0,
                decimalPart: '',
            };
        }

        if (decimalPart.length > 4) {
            const zeroCount = decimalPart.search(/[^0]/);
            const rounded = roundFn(parseFloat(decimalPart.slice(0, 5)) / 10);
            const zeroString = zeroCount > 0 ? '0'.repeat(zeroCount) : '';
            return {
                integerPart: `${negative ? '-' : ''}${integerPart}`,
                zeroCount: 0,
                decimalPart: (zeroString + rounded.toString()).replace(/0+$/, ''),
            };
        }
        return {
            integerPart: `${negative ? '-' : ''}${str}`,
            zeroCount: 0,
            decimalPart: '',
        };
    }
}

export const formatMoney = (amount?: number | string, showCurrency = true, unit = ''): string => {
    // treat null/undefined as zero
    if (amount == null) return '0';

    // coerce to a JS number once
    const raw = typeof amount === 'number' ? amount : parseFloat(`${amount}`);

    if (!isFinite(raw)) {
        // if it’s not a valid number, return “0” or you could throw
        return '0';
    }

    // reuse our safer formatter
    const formatter = (n: number) => parseFloat(n.toFixed(1)).toLocaleString();

    let formatted: string;

    if (raw >= 1e15) {
        formatted = `${formatter(raw / 1e15)}Q`; // quadrillions
    } else if (raw >= 1e12) {
        formatted = `${formatter(raw / 1e12)}T`; // trillions
    } else if (raw >= 1e9) {
        formatted = `${formatter(raw / 1e9)}B`; // billions
    } else if (raw >= 1e6) {
        formatted = `${formatter(raw / 1e6)}M`; // millions
    } else if (raw >= 1e3) {
        formatted = `${formatter(raw / 1e3)}K`; // thousands
    } else if (raw < 1) {
        // small decimals: up to 8 places, trimming trailing zeros
        formatted = raw
            .toFixed(8)
            .replace(/\.0+$/, '')
            .replace(/(\.\d+?)0+$/, '$1');
    } else {
        formatted = formatter(raw);
    }

    let result = showCurrency ? `$${formatted}` : formatted;

    if (unit) {
        result += unit;
    }

    return result;
};

export const formatElapsedTimeFromUtc = (startUtc: string): string => {
    const now = dayjs.utc();
    const past = dayjs.utc(startUtc);

    let diffSeconds = now.diff(past, 'second');

    const years = Math.floor(diffSeconds / (365 * 24 * 60 * 60));
    diffSeconds %= 365 * 24 * 60 * 60;

    const days = Math.floor(diffSeconds / (24 * 60 * 60));
    diffSeconds %= 24 * 60 * 60;

    const hours = Math.floor(diffSeconds / (60 * 60));
    diffSeconds %= 60 * 60;

    const minutes = Math.floor(diffSeconds / 60);
    const seconds = diffSeconds % 60;

    return `${years}Y ${days}D ${hours}h ${minutes}m ${seconds}s`;
};

export const getFeeSol = (sol: number, maxComputeUnits: number) => {
    if (sol) {
        return formatBalanceWallet({
            balance: (sol * maxComputeUnits) / Math.pow(10, 15),
            decimal: 6,
        });
    }
    return 0;
};
export const formatBalanceWallet = (options: { balance: number; decimal: number }): number => {
    const { balance, decimal } = options;
    if (balance == null || !isFinite(balance)) return 0;
    const val = parseFloat(balance.toFixed(decimal));
    return val;
};

export const solToLamport = (sol: number) => new Decimal(sol).mul(1e9).toFixed(0);
export const lamportToSol = (lamport: number) => new Decimal(lamport).div(1e9).toFixed(9);

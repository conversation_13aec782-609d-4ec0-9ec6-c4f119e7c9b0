import { DynamicModule, Global, Module } from '@nestjs/common';
import { RedisService } from './redis.service';
import Redis from 'ioredis';
import { ConfigModule, ConfigService } from '@nestjs/config';

export interface RedisModuleOptions {
    host?: string;
    port?: number;
    password?: string;
    db?: number;
    url?: string;
    keyPrefix?: string;
}

@Global()
@Module({})
export class RedisModule {
    static register(options: RedisModuleOptions = {}): DynamicModule {
        return {
            module: RedisModule,
            providers: [
                {
                    provide: 'REDIS_CLIENT',
                    useFactory: () => {
                        return options.url
                            ? new Redis(options.url)
                            : new Redis({
                                  host: options.host || 'localhost',
                                  port: options.port || 6379,
                                  password: options.password || undefined,
                                  db: options.db || 0,
                                  keyPrefix: options.keyPrefix || '',
                              });
                    },
                },
                RedisService,
            ],
            exports: [RedisService],
        };
    }

    static registerAsync(): DynamicModule {
        return {
            module: RedisModule,
            imports: [ConfigModule],
            providers: [
                {
                    provide: 'REDIS_CLIENT',
                    useFactory: (configService: ConfigService) => {
                        const REDIS_HOST = configService.get<string>('REDIS_HOST') || 'localhost';
                        const REDIS_PORT = parseInt(configService.get<string>('REDIS_PORT') || '6379', 10);
                        const REDIS_PASSWORD = configService.get<string>('REDIS_PASS');
                        const REDIS_DB = parseInt(configService.get<string>('REDIS_DB') || '0', 10);
                        const REDIS_KEY_PREFIX = configService.get<string>('REDIS_KEY_PREFIX') || '';
                        const REDIS_URL = configService.get<string>('REDIS_URL');

                        if (REDIS_URL) {
                            return new Redis(REDIS_URL);
                        }

                        return new Redis({
                            host: REDIS_HOST,
                            port: REDIS_PORT,
                            password: REDIS_PASSWORD,
                            db: REDIS_DB,
                            keyPrefix: REDIS_KEY_PREFIX,
                        });
                    },
                    inject: [ConfigService],
                },
                RedisService,
            ],
            exports: [RedisService],
        };
    }
}

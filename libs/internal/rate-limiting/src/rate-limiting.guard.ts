import { Injectable, CanActivate, ExecutionContext, SetMetadata } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { RateLimitingService, RateLimitConfig } from './rate-limiting.service';
import { ApiError } from 'libs/common/api-errors';
import { RATE_LIMIT_EXCEEDED, EMAIL_OTP_RATE_LIMITED, LOGIN_RATE_LIMITED } from 'libs/common/api-errors/errors';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import * as crypto from 'crypto';

export const RATE_LIMIT_KEY = 'rateLimit';

export interface RateLimitOptions {
    windowMs: number; // Time window in milliseconds
    maxRequests: number; // Maximum requests per window
    keyPrefix: string; // Redis key prefix for this rate limit type
    skipSuccessfulRequests?: boolean; // Whether to skip counting successful requests
    skipFailedRequests?: boolean; // Whether to skip counting failed requests
    errorType?: 'EMAIL_OTP' | 'LOGIN' | 'GENERIC'; // Type of error to throw when rate limited
}

/**
 * Decorator to apply rate limiting to a method
 * @param options Rate limiting configuration
 */
export const RateLimit = (options: RateLimitOptions) => SetMetadata(RATE_LIMIT_KEY, options);

@Injectable()
export class RateLimitGuard implements CanActivate {
    constructor(
        private readonly rateLimitingService: RateLimitingService,
        private readonly reflector: Reflector,
        @InjectPinoLogger(RateLimitGuard.name)
        private readonly logger: PinoLogger,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const rateLimitOptions = this.reflector.get<RateLimitOptions>(RATE_LIMIT_KEY, context.getHandler());

        if (!rateLimitOptions) {
            // No rate limiting configured for this handler
            return true;
        }

        const gqlContext = GqlExecutionContext.create(context);
        const { req } = gqlContext.getContext();

        // Extract IP address from request
        const ipAddress = this.extractIpAddress(req);

        if (!ipAddress) {
            this.logger.warn('Could not extract IP address from request');
            // If we can't get IP, allow the request but log the issue
            return true;
        }

        const config: RateLimitConfig = {
            windowMs: rateLimitOptions.windowMs,
            maxRequests: rateLimitOptions.maxRequests,
            keyPrefix: rateLimitOptions.keyPrefix,
        };

        try {
            const result = await this.rateLimitingService.checkRateLimit(ipAddress, config);

            // Add rate limit headers to response (if supported by GraphQL context)
            this.addRateLimitHeaders(req, result, config);

            if (!result.allowed) {
                this.logger.warn(
                    {
                        ipAddress: this.hashIpForLogging(ipAddress),
                        totalHits: result.totalHits,
                        maxRequests: config.maxRequests,
                        windowMs: config.windowMs,
                        keyPrefix: config.keyPrefix,
                        resetTime: new Date(result.resetTime).toISOString(),
                    },
                    'Rate limit exceeded',
                );

                // Choose appropriate error based on the rate limit type
                const errorType = rateLimitOptions.errorType || 'GENERIC';
                let error;
                switch (errorType) {
                    case 'EMAIL_OTP':
                        error = new ApiError(EMAIL_OTP_RATE_LIMITED);
                        break;
                    case 'LOGIN':
                        error = new ApiError(LOGIN_RATE_LIMITED);
                        break;
                    default:
                        error = new ApiError(RATE_LIMIT_EXCEEDED);
                        break;
                }
                throw error;
            }

            this.logger.debug(
                {
                    ipAddress: this.hashIpForLogging(ipAddress),
                    totalHits: result.totalHits,
                    remainingRequests: result.remainingRequests,
                    keyPrefix: config.keyPrefix,
                },
                'Rate limit check passed',
            );

            return true;
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }

            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    ipAddress: this.hashIpForLogging(ipAddress),
                    config,
                },
                'Error in rate limit guard',
            );

            // Fail open - allow the request if there's an unexpected error
            return true;
        }
    }

    /**
     * Extract IP address from request with fallbacks
     * Prioritizes Cloudflare's CF-Connecting-IP header for trusted proxy environments
     */
    private extractIpAddress(req: any): string | null {
        // Try various headers and properties to get the real IP
        // CF-Connecting-IP is prioritized first for Cloudflare proxy environments
        const candidates = [
            req.headers['cf-connecting-ip'], // Cloudflare real client IP (highest priority)
            req.ip, // Express req.ip
            req.headers['x-forwarded-for']?.split(',')[0]?.trim(), // Standard proxy header
            req.headers['x-real-ip'], // Nginx real IP
            req.headers['x-client-ip'], // Alternative client IP header
            req.connection?.remoteAddress, // Direct connection address
            req.socket?.remoteAddress, // Socket remote address
            req.connection?.socket?.remoteAddress, // Nested socket address
        ];

        // Log all IP extraction candidates for debugging
        this.logger.debug(
            {
                'cf-connecting-ip': req.headers['cf-connecting-ip'], // Cloudflare real client IP
                ip: req.ip,
                'x-forwarded-for': req.headers['x-forwarded-for'],
                'x-real-ip': req.headers['x-real-ip'],
                'x-client-ip': req.headers['x-client-ip'],
                'connection.remoteAddress': req.connection?.remoteAddress,
                'socket.remoteAddress': req.socket?.remoteAddress,
                'connection.socket.remoteAddress': req.connection?.socket?.remoteAddress,
            },
            'Extracting IP address from request',
        );

        // log req.headers
        this.logger.debug({ headers: req.headers }, 'Request headers');

        for (const candidate of candidates) {
            if (candidate && typeof candidate === 'string' && candidate.trim()) {
                // Clean up IPv6 mapped IPv4 addresses
                const cleaned = candidate.replace(/^::ffff:/, '');
                if (this.isValidIp(cleaned)) {
                    return cleaned;
                }
            }
        }

        return null;
    }

    /**
     * Basic IP address validation
     */
    private isValidIp(ip: string): boolean {
        // Simple regex for IPv4 and IPv6 validation
        const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

        return ipv4Regex.test(ip) || ipv6Regex.test(ip) || ip === 'localhost' || ip === '127.0.0.1';
    }

    /**
     * Hash IP for logging purposes (different salt than rate limiting)
     */
    private hashIpForLogging(ip: string): string {
        return crypto
            .createHash('sha256')
            .update(ip + 'logging_salt')
            .digest('hex')
            .substring(0, 8);
    }

    /**
     * Add rate limit information to request for potential response headers
     */
    private addRateLimitHeaders(req: any, result: any, config: RateLimitConfig): void {
        // Store rate limit info in request for potential use in response
        req.rateLimit = {
            limit: config.maxRequests,
            remaining: result.remainingRequests,
            reset: new Date(result.resetTime),
            total: result.totalHits,
        };
    }
}

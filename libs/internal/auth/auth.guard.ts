import { ExecutionContext, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AUTH_AGENT, AUTH_HEADER } from './auth.constant';
import { extractJwtPayload } from '../../utils/jwt.util';

@Injectable()
export class GqlAuthGuard extends AuthGuard('jwt') {
    getRequest(context: ExecutionContext) {
        const ctx = GqlExecutionContext.create(context);
        return ctx.getContext().req;
    }

    canActivate(context: ExecutionContext) {
        const ctx = GqlExecutionContext.create(context);
        const { req } = ctx.getContext();

        if (req.headers[AUTH_HEADER] !== AUTH_AGENT) {
            return false;
        }

        const token = req.headers.authorization?.split(' ')[1];
        req.user = token ? extractJwtPayload(token) : null;

        return !!req.user;
    }
}

import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ethers } from 'ethers';
import { UsersService } from '../users/users.service';
import { ApiError } from '../../common/api-errors';
import {
    CHAIN_TYPE_NOT_SUPPORTED,
    INVALID_REFRESH_TOKEN_ERROR,
    INVALID_SIGNATURE_ERROR,
} from '../../common/api-errors/errors';
import { NonceStore } from './nonce.store';
import { AuthProvider } from '../users/entities/user.entity';
import { appConfig } from '../../configs';
import { LoginDTO, RefreshAccessTokenDTO } from './dto/auth.dto';
import { LoginArgs } from './dto/login.arg';
import { ChainType } from '../users/entities/user-managed-wallet.entity';
import { PublicKey } from '@solana/web3.js';
import * as nacl from 'tweetnacl';
import { TronWeb } from 'tronweb';
import { JwtClaim } from './auth.vo';
import { AUTH_AGENT } from './auth.constant';

@Injectable()
export class WalletAuthService {
    constructor(
        private readonly jwtService: JwtService,
        private userService: UsersService,
        private nonceStore: NonceStore,
    ) {}

    generateNonce(walletAddress: string): string {
        const nonce = ethers.hexlify(ethers.randomBytes(16));
        this.nonceStore.setNonce(walletAddress, nonce);
        return nonce;
    }

    getSignMessage(walletAddress: string, nonce: string): string {
        let message = 'Welcome to Xbit!\n\n';
        message += 'Click to sign in and accept the Xbit Terms of Service https://xbit.com/terms-of-use\n\n';
        message += 'This request will not trigger a blockchain transaction or cost any gas fees.\n\n';
        message += `Wallet address:\n\n${walletAddress}\n\n`;
        message += `Nonce:\n${nonce}`;
        return message;
    }
    okxMessageToSign(address: string, nonce: string): string {
        let message = 'Welcome to Xbit!  ';
        message += 'Click to sign in and accept the Xbit Terms of Service https://xbit.com/terms-of-use  ';
        message += 'This request will not trigger a blockchain transaction or cost any gas fees.  ';
        message += `Wallet address: ${address}  `;
        message += `Nonce: ${nonce}`;
        return message.trim();
    }

    extractWalletAddress(message: string): string {
        const lines = message.split('\n\n');
        return lines[4];
    }

    extractOkxWalletAddress(message: string): string {
        const match = message.match(/Wallet address:\s*(\S+)/) || [''];
        if (match.length <= 1) {
            throw new ApiError(INVALID_SIGNATURE_ERROR);
        }
        return match[1];
    }

    verifyEVMSignature(message: string, signature: string, walletAddress: string): boolean {
        const recoveredAddress = ethers.verifyMessage(message, signature);
        return recoveredAddress.toLowerCase() === walletAddress.toLowerCase();
    }

    verifySolSignature(message: string, signature: string, walletAddress: string): boolean {
        try {
            const signatureUint8 = Buffer.from(signature, 'base64');
            const publicKey = new PublicKey(walletAddress);
            const messageUint8 = new TextEncoder().encode(message);
            return nacl.sign.detached.verify(messageUint8, signatureUint8, publicKey.toBytes());
        } catch (_) {
            return false;
        }
    }

    async verifyTrxSignature(message: string, signature: string, walletAddress: string): Promise<boolean> {
        const tronWeb = new TronWeb({ fullHost: 'https://api.trongrid.io' });
        const base58Address = await tronWeb.trx.verifyMessageV2(message, signature);
        return base58Address.toLowerCase() === walletAddress.toLowerCase();
    }

    async login(data: LoginArgs): Promise<LoginDTO> {
        const walletAddress = data.isOkxWallet
            ? this.extractOkxWalletAddress(data.message)
            : this.extractWalletAddress(data.message);
        let authProvider: AuthProvider;
        switch (data.chainType) {
            case ChainType.EVM:
                if (!this.verifyEVMSignature(data.message, data.signature, walletAddress)) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                authProvider = AuthProvider.CHAIN_EVM;
                break;
            case ChainType.SOLANA:
                if (!this.verifySolSignature(data.message, data.signature, walletAddress)) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                authProvider = AuthProvider.CHAIN_SOL;
                break;
            case ChainType.TRON: {
                authProvider = AuthProvider.CHAIN_TRON;
                const isValid = await this.verifyTrxSignature(data.message, data.signature, walletAddress);
                if (!isValid) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                break;
            }
            default:
                throw new ApiError(CHAIN_TYPE_NOT_SUPPORTED);
        }
        const user = await this.userService.getOrCreateUserByWallet(
            walletAddress,
            authProvider,
            data.referrerCode,
            data.fingerprint,
        );
        const payload: JwtClaim = {
            sub: user.id,
            iss: AUTH_AGENT,
        };
        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.jwtService.signAsync(payload, {
                secret: appConfig.JWT_REFRESH_SECRET,
                expiresIn: appConfig.REFRESH_TOKEN_EXPIRES_IN,
            }),
            userId: user.id,
        } as LoginDTO;
    }

    async refreshAccessToken(refreshToken: string): Promise<RefreshAccessTokenDTO> {
        try {
            const payload = await this.jwtService.verifyAsync(refreshToken, {
                secret: appConfig.JWT_REFRESH_SECRET,
            });

            delete payload?.iat;
            delete payload?.exp;

            const accessToken = await this.jwtService.signAsync(payload);

            return { accessToken };
        } catch (error) {
            throw new ApiError(INVALID_REFRESH_TOKEN_ERROR);
        }
    }
}

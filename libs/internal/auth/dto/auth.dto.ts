import { Field, InputType, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class LoginDTO {
    @Field()
    accessToken: string;
    @Field()
    refreshToken: string;
    @Field()
    userId: string;
    @Field(() => String, { nullable: true })
    referrerCode?: string;
    @Field(() => String, { nullable: true })
    fingerprint?: string;
}

@ObjectType()
export class RefreshAccessTokenDTO {
    @Field()
    accessToken: string;
}

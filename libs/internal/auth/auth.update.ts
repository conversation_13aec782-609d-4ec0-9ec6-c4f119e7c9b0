import { Update, Ctx, Start, Hears, Sender, Action, Settings, InjectBot } from 'nestjs-telegraf';
import { Context, Markup, Telegraf } from 'telegraf';
import { UsersService } from '../users/users.service';
import { CachingService } from '../caching/caching.service';
import { TelegramUserDTO } from '../users/dto/telegram-user.dto';
import { appConfig } from '../../configs';
import { ChainType, UserManagedWallet } from '../users/entities/user-managed-wallet.entity';
import { EntityManager } from '@mikro-orm/core';
import { getArbBalance, getEvmBalance, getSolanaBalance } from '../users/wallet.utils';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AuthUpdate {
    constructor(
        private userService: UsersService,
        private cachingService: CachingService,
        private readonly em: EntityManager,
        @InjectBot(appConfig.TELEGRAM_BOT_AUTH_NAME) private readonly bot: Telegraf,
    ) {
        this.bot.command('start', async (ctx) => {
            await this.start(ctx);
        });
    }

    async start(
        @Ctx() ctx: Context,
        // @Sender('id') id: string,
        // @Sender('username') username: string,
        // @Sender('first_name') firstName: string,
        // @Sender('last_name') lastName: string,
    ) {
        const sender = ctx.from;
        const userDto: TelegramUserDTO = {
            // id,
            // username,
            // firstName,
            // lastName,
            id: sender?.id.toString() || '',
            username: sender?.username || '',
            firstName: sender?.first_name || '',
            lastName: sender?.last_name || '',
            chatId: ctx.chat?.id,
        };

        const { user, isNewUser } = await this.userService.getOrCreateUserByTelegramAccount(userDto);
        const code = await this.cachingService.generateAuthCode(user.id);
        const wallets = await this.em.fork().find(UserManagedWallet, { user: user });
        const solana: any = { balance: 0 };
        const arb: any = { balance: 0 };
        const evm: any = { balance: 0 };
        for (const wallet of wallets) {
            if (wallet.chain == ChainType.EVM) {
                evm.walletAddress = wallet.walletAddress;
            }
            if (wallet.chain == ChainType.SOLANA) {
                solana.walletAddress = wallet.walletAddress;
            }
            if (wallet.chain == ChainType.ARB) {
                arb.walletAddress = wallet.walletAddress;
            }
        }
        if (!isNewUser) {
            solana.balance = await getSolanaBalance(solana.walletAddress);
            evm.balance = await getEvmBalance(evm.walletAddress);
            arb.balance = await getArbBalance(evm.walletAddress);
        }

        const urlLogin = `${appConfig.TELEGRAM_LOGIN_URL}?code=${code}&user_id=${user.id}`;
        await ctx.replyWithHTML(
            `Log in to Xbit for trading in seconds 🤘🏻\n\n` +
                `💳 <b>Solana</b>: ${solana.balance} SOL (Please top up 👇🏻)\n` +
                `<code>${solana.walletAddress}</code> (Tap to copy) Trade Bot\n\n` +
                `💳 <b>Ethereum</b>: ${evm.balance} ETH (Please top up 👇🏻)\n` +
                `<code>${evm.walletAddress}</code> (Tap to copy) Trade Bot\n\n` +
                `💳 <b>Arbitrum</b>: ${arb.balance} ETH (Please top up 👇🏻)\n` +
                `<code>${arb.walletAddress}</code> (Tap to copy) Trade Bot\n\n` +
                `🔗 <b>Reflink</b>\n` +
                `<a href="${appConfig.LANDING_PAGE_URL}">${appConfig.LANDING_PAGE_URL}</a> (Tap to copy)\n`,
            // `<a href="https://xbit.ai/?ref=${user.id}">https://xbit.ai/?ref=${user.id}</a> (Tap to copy)`, // todo: need to replace link
            Markup.inlineKeyboard([Markup.button.url('👉 Login Website', urlLogin)]),
        );
    }

    @Action('button1')
    act1(ctx: Context) {
        return `Hey 1`;
    }

    @Hears(['hi', 'hello', 'hey', 'qq'])
    onGreetings(@Sender('first_name') firstName: string): string {
        return `Hey ${firstName}`;
    }
}

function escapeMarkdownV2(text: string): string {
    return text.replace(/[_*[\]()~`>#+\-=|{}.!]/g, '\\$&');
}

import { Entity, PrimaryKey, Property } from '@mikro-orm/core';
import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { v4 as uuidv4 } from 'uuid';

export enum NotificationTypeCategoryCode {
    SmartMoneyActivity = 'SmartMoneyActivity',
    PriceChange = 'PriceChange',
    FuturesSignal = 'FuturesSignal',
}

registerEnumType(NotificationTypeCategoryCode, {
    name: 'NotificationTypeCategoryCode',
    description: 'Notification type category code',
});

@ObjectType()
@Entity({ tableName: 'notification_types' })
export class NotificationType {
    @Field()
    @PrimaryKey({ type: 'uuid', defaultRaw: 'gen_random_uuid()' })
    id: string = uuidv4();

    @Field()
    @Property({ type: 'varchar', length: 100, unique: true })
    categoryCode: string;

    @Field()
    @Property({ type: 'varchar', length: 255 })
    categoryName: string;

    @Field({ nullable: true })
    @Property({ type: 'text', nullable: true })
    description?: string;

    @Field()
    @Property({ type: 'timestamptz', defaultRaw: 'now()' })
    createdAt: Date = new Date();

    @Field()
    @Property({ type: 'timestamptz', defaultRaw: 'now()', onUpdate: () => new Date() })
    updatedAt: Date = new Date();

    @Field()
    @Property({ type: 'timestamptz', nullable: true })
    deletedAt?: Date;
}

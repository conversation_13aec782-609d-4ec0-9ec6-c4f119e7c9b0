import { EntityManager, EntityRepository } from '@mikro-orm/core';
import { User } from '../entities/user.entity';
import { UserManagedWallet } from '../entities/user-managed-wallet.entity';
import { ManagedWalletDto } from '../dto/managed-wallet.dto';

export class UserManagedWalletRepository extends EntityRepository<UserManagedWallet> {
    async createWalletsForUser(
        em: EntityManager,
        user: User,
        wallets: ManagedWalletDto[],
    ): Promise<UserManagedWallet[]> {
        // const em = this.em.fork();
        const result: UserManagedWallet[] = [];
        for (const wallet of wallets) {
            const w = new UserManagedWallet();
            w.user = user;
            w.chain = wallet.chain;
            w.walletAddress = wallet.walletAddress;
            w.encryptedPrivateKey = wallet.encryptedPrivateKey;
            await em.persistAndFlush(w);
            result.push(w);
        }
        return result;
    }
}

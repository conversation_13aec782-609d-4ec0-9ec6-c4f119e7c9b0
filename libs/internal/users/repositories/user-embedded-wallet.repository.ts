import { EntityManager, EntityRepository } from '@mikro-orm/core';
import { User } from '../entities/user.entity';
import { UserEmbeddedWallet } from '../entities/user-embedded-wallet.entity';
import { EmbeddedWalletDto } from '../dto/embedded-wallet.dto';
import { ApiError } from 'libs/common/api-errors';
import { WALLET_NOT_FOUND, WALLET_ACCESS_DENIED } from 'libs/common/api-errors/errors';

export class UserEmbeddedWalletRepository extends EntityRepository<UserEmbeddedWallet> {
    async createWalletsForUser(
        em: EntityManager,
        user: User,
        wallets: EmbeddedWalletDto[],
    ): Promise<UserEmbeddedWallet[]> {
        // const em = this.em.fork();
        const result: UserEmbeddedWallet[] = [];
        for (const wallet of wallets) {
            const w = new UserEmbeddedWallet();
            w.user = user;
            w.chain = wallet.chain;
            w.walletAddress = wallet.walletAddress;
            w.walletAccountId = wallet.walletAccountId;
            w.walletId = wallet.walletId;
            w.hdPath = wallet.hdPath;
            w.name = wallet.name;
            await em.persistAndFlush(w);
            result.push(w);
        }
        return result;
    }

    async updateWalletName(
        wallet: UserEmbeddedWallet,
        em: EntityManager,
        user: User,
        newName: string,
    ): Promise<UserEmbeddedWallet> {
        if (!wallet) {
            throw new ApiError(WALLET_NOT_FOUND);
        }

        // Additional security check to ensure the wallet belongs to the user
        if (wallet.user.id !== user.id) {
            throw new ApiError(WALLET_ACCESS_DENIED);
        }

        wallet.name = newName;
        await em.persistAndFlush(wallet);

        return wallet;
    }
}

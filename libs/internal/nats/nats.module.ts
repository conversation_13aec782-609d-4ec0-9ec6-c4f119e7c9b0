import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { NatsService } from './nats.service';
import { getEnv } from 'libs/configs/env';
import { connect, NatsConnection } from 'nats';

@Global()
@Module({})
export class NatsModule {
    static registerAsync() {
        return {
            module: NatsModule,
            imports: [ConfigModule],
            providers: [
                {
                    provide: 'NATS_CLIENT',
                    useFactory: async () => {
                        const natUrls = getEnv('NATS_URL').split(',');
                        const token = getEnv('NATS_AUTH_TOKEN');
                        const v = {
                            servers: natUrls,
                            token: token,
                        };
                        const nc: NatsConnection = await connect(v);
                        return nc;
                    },
                },
                NatsService,
            ],
            exports: [NatsService],
        };
    }
}

import { Inject, Injectable } from '@nestjs/common';
import { JetStreamClient, NatsConnection } from 'nats';

@Injectable()
export class NatsService {
    private js: JetStreamClient;
    constructor(@Inject('NATS_CLIENT') private readonly natsClient: NatsConnection) {
        this.js = natsClient.jetstream();
    }

    publish<T>(subject: string, data: T) {
        this.js.publish(subject, JSON.stringify(data));
    }

    getClient() {
        return this.natsClient;
    }
}

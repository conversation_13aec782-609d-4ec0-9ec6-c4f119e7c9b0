#!/usr/bin/env node

/**
 * Test script for OTP verification throttling functionality
 * This script tests the OTP verification throttling to prevent brute force attacks
 */

const axios = require('axios');

const GRAPHQL_ENDPOINT = 'http://localhost:3000/graphql';

const INIT_EMAIL_OTP_MUTATION = `
  mutation InitEmailOtp($input: InitEmailOtpInputDTO!) {
    initEmailOtp(input: $input) {
      otpId
      userId
      subOrgId
      ttl
    }
  }
`;

const LOGIN_EMAIL_OTP_MUTATION = `
  mutation LoginWithEmailOtp($input: LoginWithEmailOtpInputDTO!) {
    loginWithEmailOtp(input: $input) {
      accessToken
      refreshToken
      userId
      subOrgId
    }
  }
`;

async function makeGraphQLRequest(query, variables) {
  try {
    const response = await axios.post(GRAPHQL_ENDPOINT, {
      query,
      variables,
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    return response.data;
  } catch (error) {
    if (error.response) {
      return error.response.data;
    }
    throw error;
  }
}

async function testOtpThrottling() {
  console.log('🧪 Testing OTP Verification Throttling...\n');

  // Step 1: Initialize OTP
  console.log('📧 Step 1: Initializing OTP...');
  const initResult = await makeGraphQLRequest(INIT_EMAIL_OTP_MUTATION, {
    input: {
      email: '<EMAIL>',
      fingerprint: 'test-fingerprint-' + Date.now()
    }
  });

  if (initResult.errors) {
    console.error('❌ Failed to initialize OTP:', initResult.errors);
    return;
  }

  const otpId = initResult.data.initEmailOtp.otpId;
  console.log('✅ OTP initialized successfully. OTP ID:', otpId);
  console.log('');

  // Step 2: Test multiple failed attempts
  console.log('🔄 Step 2: Testing multiple failed OTP verification attempts...');
  
  const maxAttempts = 6; // Should be throttled after 5 attempts
  const wrongOtpCode = '000000'; // Wrong OTP code

  for (let i = 1; i <= maxAttempts; i++) {
    console.log(`\n🔍 Attempt ${i}/${maxAttempts}: Trying with wrong OTP code...`);
    
    const loginResult = await makeGraphQLRequest(LOGIN_EMAIL_OTP_MUTATION, {
      input: {
        email: '<EMAIL>',
        otpId: otpId,
        otpCode: wrongOtpCode,
        targetPublicKey: '0x1234567890abcdef'
      }
    });

    if (loginResult.errors) {
      const error = loginResult.errors[0];
      console.log(`❌ Attempt ${i} failed:`, error.message);
      
      if (error.extensions?.code === 'OTP_VERIFICATION_THROTTLED') {
        console.log('🚫 THROTTLING ACTIVATED! Further attempts are blocked.');
        console.log('⏰ Wait time specified in error message:', error.message);
        break;
      }
    } else {
      console.log(`✅ Attempt ${i} succeeded (unexpected!)`);
    }

    // Small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('\n📊 Test Summary:');
  console.log('- OTP verification throttling should activate after 5 failed attempts');
  console.log('- Throttled requests should be blocked for 15 minutes');
  console.log('- Error message should specify exact wait time');
  console.log('\n✅ OTP Throttling test completed!');
}

async function testThrottlingWithDifferentOtpIds() {
  console.log('\n🧪 Testing throttling isolation between different OTP IDs...\n');

  // Initialize two different OTPs
  const initResult1 = await makeGraphQLRequest(INIT_EMAIL_OTP_MUTATION, {
    input: {
      email: '<EMAIL>',
      fingerprint: 'test-fingerprint-1-' + Date.now()
    }
  });

  const initResult2 = await makeGraphQLRequest(INIT_EMAIL_OTP_MUTATION, {
    input: {
      email: '<EMAIL>', 
      fingerprint: 'test-fingerprint-2-' + Date.now()
    }
  });

  if (initResult1.errors || initResult2.errors) {
    console.error('❌ Failed to initialize OTPs');
    return;
  }

  const otpId1 = initResult1.data.initEmailOtp.otpId;
  const otpId2 = initResult2.data.initEmailOtp.otpId;

  console.log('✅ Two OTPs initialized:');
  console.log('  - OTP ID 1:', otpId1);
  console.log('  - OTP ID 2:', otpId2);

  // Make 5 failed attempts on first OTP to trigger throttling
  console.log('\n🔄 Making 5 failed attempts on first OTP...');
  for (let i = 1; i <= 5; i++) {
    await makeGraphQLRequest(LOGIN_EMAIL_OTP_MUTATION, {
      input: {
        email: '<EMAIL>',
        otpId: otpId1,
        otpCode: '000000',
        targetPublicKey: '0x1234567890abcdef'
      }
    });
  }

  // Test that first OTP is throttled
  console.log('\n🚫 Testing that first OTP is now throttled...');
  const throttledResult = await makeGraphQLRequest(LOGIN_EMAIL_OTP_MUTATION, {
    input: {
      email: '<EMAIL>',
      otpId: otpId1,
      otpCode: '000000',
      targetPublicKey: '0x1234567890abcdef'
    }
  });

  const isThrottled = throttledResult.errors && 
    throttledResult.errors[0].extensions?.code === 'OTP_VERIFICATION_THROTTLED';

  console.log(isThrottled ? '✅ First OTP is correctly throttled' : '❌ First OTP should be throttled');

  // Test that second OTP is still working
  console.log('\n🔍 Testing that second OTP is still working...');
  const secondOtpResult = await makeGraphQLRequest(LOGIN_EMAIL_OTP_MUTATION, {
    input: {
      email: '<EMAIL>',
      otpId: otpId2,
      otpCode: '000000',
      targetPublicKey: '0x1234567890abcdef'
    }
  });

  const isSecondOtpWorking = secondOtpResult.errors && 
    secondOtpResult.errors[0].extensions?.code === 'OTP_VERIFICATION_FAILED';

  console.log(isSecondOtpWorking ? 
    '✅ Second OTP is working (gets OTP_VERIFICATION_FAILED, not throttled)' : 
    '❌ Second OTP should not be throttled');

  console.log('\n✅ OTP ID isolation test completed!');
}

// Run tests
async function runTests() {
  try {
    await testOtpThrottling();
    await testThrottlingWithDifferentOtpIds();
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

if (require.main === module) {
  runTests();
}

module.exports = { testOtpThrottling, testThrottlingWithDifferentOtpIds };

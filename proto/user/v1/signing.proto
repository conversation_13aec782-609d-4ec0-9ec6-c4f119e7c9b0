syntax = "proto3";

package user.signing.v1;

service UserSigningService {
  rpc SignEvmTransaction(SignUserTransactionEvmRequest) returns (SignUserTransactionEvmResponse) {}
  rpc SignTransaction(SignUserTransactionRequest) returns (SignUserTransactionResponse) {}
  rpc ApproveWithdrawTransaction(ApproveWithdrawRequest) returns (ApproveWithdrawResponse) {}
}

enum ChainType {
  EVM = 0;
  SOLANA = 1;
  TRON = 2;
  ARB = 3;
}

message SignUserTransactionEvmRequest {
  string user_id = 1;
  string to = 2;
  string data = 3;
  optional string value = 4;
  ChainType chain = 5;
  optional string maxFeePerGas = 6;
  optional string maxPriorityFeePerGas = 7;
  optional string gasLimit = 8;
  optional string gasPrice = 9;
  string from = 10;
}
  

message SignUserTransactionEvmResponse {
  string user_id = 1;
  string to = 2;
  string data = 3;
  optional string value = 4;
  ChainType chain = 5;
  optional string maxFeePerGas = 6;
  optional string maxPriorityFeePerGas = 7;
  optional string gasLimit = 8;
  optional string gasPrice = 9;
  string from = 10;
  string signature = 11;
}

message SignUserTransactionRequest {
  string user_id = 1;
  string unsigned_transaction = 2;
  ChainType chain = 3;
  string address = 4;
}

message SignUserTransactionResponse {
  string signed_transaction = 1;
}

message ApproveWithdrawRequest {
  string user_id = 1;
  ChainType chain = 2;
  string address = 3;
  string receive_address = 4;
  string amount = 5;
  string activityId = 6;
  string token = 7;
  int32 decimals = 8;
  optional string signature = 9;
  optional string message = 10;
  optional bool isOkxWallet = 11;
  optional string oidcToken = 12;
  optional string otpId = 13;
  optional string otpCode = 14;
}

message ApproveWithdrawResponse {
  string signed_transaction = 1;
}
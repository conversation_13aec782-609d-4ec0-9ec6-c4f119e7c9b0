syntax = "proto3";

package user.user_info.v1;

service UserInfoService{
    rpc GetUserInfo(GetUserInfoRequest) returns (UserInfoResponse){}
    rpc GetUserWalletManaged(GetUserInfoRequest) returns (WalletManagedResponse){}
    rpc GetUserBalance(GetUserBalanceRequest) returns (UserBanlanceResponse){}
    rpc GetAccessToken(GetUserInfoRequest) returns (GetAccessTokenResponse){}
    rpc GetAuthCode(GetUserInfoRequest) returns (AuthCodeResponse){}
    rpc GetUserReferrerCode(GetUserReferrerCodeRequest) returns (GetUserReferrerCodeResponse){}
}

message GetUserInfoRequest{
    string telegram_id =1;
    string user_id =2;
}
message UserInfoResponse{
    string id =1;
    // string wallet_address =2;
    optional string name =2;
    string telegram_id =3;
    optional string telegram_username =4;
    int64 telegram_chat_id=5;
}

message GetUserWalletManagedRequest{
    string id =1;
}
enum ChainType {
    EVM = 0;
    SOLANA = 1;
    TRON = 2;
    ARB = 3;
}
message UserManagedWallet {
    string id = 1;
    string user_id = 2; 
    ChainType chain = 3;
    string wallet_address = 4;
}
message WalletManagedResponse {
    repeated UserManagedWallet wallets = 1;
}

message GetUserBalanceRequest{
     string walletAddress =1;
}

message UserBanlanceResponse{
     double balance = 1;
}

message GetAccessTokenResponse {
    string access_token = 1;
    string user_id = 2;
}

message AuthCodeResponse {
    string auth_code = 1;
    string user_id = 2;
}

message GetUserReferrerCodeRequest{}

message GetUserReferrerCodeResponse{
    string referrer_code = 1;
}